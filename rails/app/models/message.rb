class Message < ApplicationRecord
  include ActionView::Helpers::Saniti<PERSON><PERSON><PERSON><PERSON>
  attr_accessor :skip_callback
  attr_accessor :meta_data

  belongs_to :user
  belongs_to :school
  belongs_to :targetable, polymorphic: true
  belongs_to :chat_thread
  belongs_to :speech_attachment, class_name: 'Attachment', foreign_key: 'speech_attachment_id', optional: true
  belongs_to :ai_tutor_agent, optional: true

  has_many :attachments, as: :attachable

  after_create_commit :send_to_chatgpt
  after_update_commit :send_each_chatgpt

  enum owner_type: { student: 0, bot: 1, teacher: 2}

  def send_to_chatgpt
    return if skip_callback
    if self.targetable_type == "Lesson"
      lesson = Lesson.find(self.targetable_id)
      teacher_id = lesson.school.teachers.first&.id

      messages = chat_thread.messages.to_a

      message = Message.create(
        user_id: teacher_id,
        targetable_id: self.targetable_id,
        targetable_type: self.targetable_type,
        chat_thread_id: self.chat_thread_id,
        school_id: lesson.course.school_id,
        content: "",
        to_user_id: self.user_id,
        owner_type: Message.owner_types["bot"],
        skip_callback: true,
        agent_id: self.agent_id
      )

      process_id = "generating_message_#{self.user_id}_#{self.targetable_type}_#{self.targetable_id}"
      ai_tutor_agent_object = AiTutorAgent.find(self.agent_id)
      ai_tutor_prompt = ai_tutor_agent_object.ai_tutor_prompts.find_by(prompt_type: 'lesson')
      system_message_content = AiTutorPromptRenderer.build_system_message(ai_tutor_prompt, lesson, messages) if ai_tutor_prompt.present?
      SendChatSteamJob.perform_later(messages, message, process_id, system_message_content: system_message_content, agent_id: self.agent_id)

      if message.chat_thread.messages.count == 2
        message.chat_thread.update! name: message.chat_thread.messages.first.content.truncate(10)
      end
    elsif self.targetable_type == "Question"
      question = Q.find(self.targetable_id)
      school = question.exams.first.school
      teacher_id = school.teachers.first&.id
      message = Message.create(
        user_id: teacher_id,
        targetable_id: self.targetable_id,
        targetable_type: self.targetable_type,
        chat_thread_id: self.chat_thread_id,
        content: "",
        school_id: school.id,
        to_user_id: self.user_id,
        owner_type: Message.owner_types["bot"],
        skip_callback: true,
      )
      process_id = "generating_message_#{self.user_id}_#{self.targetable_type}_#{self.targetable_id}"
      exam = question.user_exams.find_by(user_id: self.user_id).exam
      meta_data = question.build_meta_data(exam, self.user_id, self.meta_data)
      system_message_content = "This is meta data of user for question and answer: #{meta_data}"
      messages = [self]
      SendChatSteamJob.perform_later(messages, message, process_id, system_message_content: system_message_content)
    elsif self.targetable_type == Exam.name
      exam = Exam.find(self.targetable_id)
      school = exam.school
      teacher_id = school.teachers.first&.id
      message = Message.create(
        user_id: teacher_id,
        targetable_id: self.targetable_id,
        targetable_type: self.targetable_type,
        chat_thread_id: self.chat_thread_id,
        content: "",
        school_id: school.id,
        to_user_id: self.user_id,
        owner_type: Message.owner_types["bot"],
        skip_callback: true,
      )
      process_id = "generating_message_#{self.user_id}_#{self.targetable_type}_#{self.targetable_id}"
      # meta_data = exam.meta_data(self.user_id)
      messages = [self]

      SendChatSteamJob.perform_later(messages, message, process_id)
    end
  end

  def get_memories target, message, bot_message
    memories = ""
    old_messages = []
    if message.chat_thread
      old_messages = message.chat_thread.messages.where(
          to_user_id: message.to_user_id,
          targetable_type: target.class.name,
          targetable_id: target.id).order(created_at: :asc).last(7)
    end
    if old_messages.any?
      old_messages.each do |m|
        next if m == message
        next if m == bot_message
        next if m.content.size == 0
        if m.bot?
          memories = memories + m.content + "@@@"
        else
          memories = memories + m.content + "@@"
        end
      end
    end
    return memories
  end

  def get_converions lesson, message
    chatgpt_messages = [
      {"role": "system", "content": "あなたはBotです。ユーザの質問に答えています。"},
    ]
    content = lesson.md_body
    content += "上記の内容について、以下の質問をします。\n"
    old_messages = Message.where(to_user_id: message.to_user_id, targetable_type: Lesson.name, targetable_id: lesson.id).order(created_at: :asc)
    if old_messages.any?
      old_messages.each do |m|
        next if m.content.size == 0
        if m == old_messages.first
          content += m.content
          chatgpt_messages.push({ role: "user", content: content})
        end
        if m.bot?
          chatgpt_messages.push({ role: "assistant", content: m.content})
        else
          chatgpt_messages.push({ role: "user", content: m.content})
        end
      end
    else
      content += message.content
      chatgpt_messages.push({ role: "user", content: content})
    end
    return chatgpt_messages
  end

  def send_each_chatgpt
    # channel = "user:#{self.to_user_id}:#{self.targetable_type}:#{self.targetable_id}:chats"
    # ActionCable.server.broadcast channel, {
    #   message_id: self.id,
    #   content: ApplicationController.helpers.emojify(UtilityHelper.markdown_to_html UtilityHelper.replace_outside_code_tags(self.content)).html_safe
    # }
  end

  def speech_attachment_url
    return speech_attachment.url(expires_in: 1.day.to_i) if speech_attachment

    client = OpenAI::Client.new
    res = client.audio.speech(
      parameters: {
        model: "tts-1",
        input: content,
        voice: "alloy",
        response_format: "opus",
        speed: 1.0
      }
    )

    transaction do
      attachment = Attachment.create_from_string(res, filename: "message-#{id}-audio.opus")
      self.speech_attachment_id = attachment.id
      self.save
    end

    speech_attachment.url(expires_in: 1.day.to_i)
  end

  def regenerate
    return unless self == chat_thread.messages.last

    self.destroy
    chat_thread.messages.reload.last&.public_send(:send_to_chatgpt)
  end

  def self.get_current_messages
    start_date = Date.today.beginning_of_month
    end_date = Date.today.end_of_month

    messages = Message.where(created_at: start_date..end_date)
    return messages.map(&:content).join("")

  end

  def self.get_messages_inrange start_date, end_date
    messages = Message.where(created_at: start_date..end_date)
    return messages.map(&:content).join("")

  end
end
